/**
 * Hotel Finder Image Effects
 * Adds interactive effects to hotel images
 */

document.addEventListener('DOMContentLoaded', function() {
    // Image hover effects for hotel detail page
    const hotelImages = document.querySelectorAll('.cascading-images-container img');
    
    // Create a container for the enlarged image
    const enlargedContainer = document.createElement('div');
    enlargedContainer.className = 'enlarged-image-container';
    enlargedContainer.style.display = 'none';
    document.body.appendChild(enlargedContainer);
    
    // Add event listeners to each image
    hotelImages.forEach(img => {
        // Mouse enter effect
        img.addEventListener('mouseenter', function() {
            // Add a subtle rotation to the image
            const randomRotation = (Math.random() * 2 - 1) * 2; // Random value between -2 and 2 degrees
            this.style.transform = `scale(1.05) rotate(${randomRotation}deg)`;
            
            // Change the z-index to bring this image to the front
            this.style.zIndex = '10';
        });
        
        // Mouse leave effect
        img.addEventListener('mouseleave', function() {
            // Reset the transform
            this.style.transform = '';
            this.style.zIndex = '';
        });
        
        // Click effect - show enlarged image
        img.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent the click from bubbling up
            
            // Create enlarged image
            enlargedContainer.innerHTML = '';
            const enlargedImg = document.createElement('img');
            enlargedImg.src = this.src;
            enlargedImg.alt = this.alt;
            enlargedImg.className = 'enlarged-image';
            
            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.className = 'close-enlarged-image';
            closeBtn.addEventListener('click', function() {
                enlargedContainer.style.display = 'none';
            });
            
            // Add to container and show
            enlargedContainer.appendChild(enlargedImg);
            enlargedContainer.appendChild(closeBtn);
            enlargedContainer.style.display = 'flex';
        });
    });
    
    // Close enlarged image when clicking outside
    document.addEventListener('click', function() {
        enlargedContainer.style.display = 'none';
    });
    
    // Prevent closing when clicking inside the enlarged image
    enlargedContainer.addEventListener('click', function(e) {
        e.stopPropagation();
    });
});
