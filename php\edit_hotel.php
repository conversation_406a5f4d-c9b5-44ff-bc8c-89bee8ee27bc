<?php
session_start();
require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/utils.php';

// Check if user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'owner') {
    header("Location: login.php");
    exit();
}

$owner_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// Check if hotel ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: profile.php");
    exit();
}

$hotel_id = $_GET['id'];

// Fetch hotel details and verify ownership
try {
    $stmt = $pdo->prepare("SELECT * FROM hotels WHERE id = ? AND owner_id = ?");
    $stmt->execute([$hotel_id, $owner_id]);
    $hotel = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$hotel) {
        // Hotel not found or doesn't belong to this owner
        header("Location: profile.php?error=not_authorized");
        exit();
    }

    // Fetch amenities if any
    $amenities = [];
    if (!empty($hotel['amenities'])) {
        $amenities = explode(',', $hotel['amenities']);
    }

} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_hotel'])) {
    // Get form data
    $name = $_POST['name'];
    $location = $_POST['location'];
    $price = $_POST['price_per_night'];
    $description = $_POST['description'];
    $contact = isset($_POST['contact']) ? $_POST['contact'] : '';
    $new_amenities = isset($_POST['amenities']) ? implode(',', $_POST['amenities']) : '';

    // Validate input
    if (empty($name) || empty($location) || empty($price) || empty($description)) {
        $error_message = "<div class='error-message'>All fields are required.</div>";
    } else {
        // Handle image upload if a new image is provided
        $image_path = $hotel['img_path']; // Keep existing image by default

        if (isset($_FILES['main_image']) && $_FILES['main_image']['error'] == 0) {
            $upload_dir = '../images/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            // Clean the filename to remove special characters
            $original_name = basename($_FILES['main_image']['name']);
            $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);

            // Create a unique filename with timestamp and sanitized hotel name
            $sanitized_name = preg_replace('/[^a-zA-Z0-9_]/', '', $name);
            $image_name = time() . '_' . $sanitized_name . '.' . $file_extension;
            $target_path = $upload_dir . $image_name;

            if (move_uploaded_file($_FILES['main_image']['tmp_name'], $target_path)) {
                $image_path = 'images/' . $image_name;
                // Make sure the path is correctly formatted
                $image_path = str_replace('\\', '/', $image_path);
                $image_path = str_replace('//', '/', $image_path);
            } else {
                $error_message = "<div class='error-message'>Error uploading image. Please try again.</div>";
            }
        }

        if (empty($error_message)) {
            try {
                // Update hotel information
                $stmt = $pdo->prepare("UPDATE hotels SET
                    hotel_name = ?,
                    location = ?,
                    price = ?,
                    description = ?,
                    contact = ?,
                    amenities = ?,
                    img_path = ?
                    WHERE id = ? AND owner_id = ?");

                $stmt->execute([
                    $name,
                    $location,
                    $price,
                    $description,
                    $contact,
                    $new_amenities,
                    $image_path,
                    $hotel_id,
                    $owner_id
                ]);

                $success_message = "<div class='success-message'>Hotel updated successfully! <a href='hotel.php?id=$hotel_id' class='view-link'>View Hotel</a></div>";

                // Refresh hotel data
                $stmt = $pdo->prepare("SELECT * FROM hotels WHERE id = ?");
                $stmt->execute([$hotel_id]);
                $hotel = $stmt->fetch(PDO::FETCH_ASSOC);

                // Update amenities array
                $amenities = [];
                if (!empty($hotel['amenities'])) {
                    $amenities = explode(',', $hotel['amenities']);
                }

            } catch (PDOException $e) {
                $error_message = "<div class='error-message'>Error: " . $e->getMessage() . "</div>";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Hotel - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        /* Add Hotel Page Styles */
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 30px;
            background-color: #1e293b;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .hotel-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-section {
            background-color: #334155;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #38bdf8;
        }

        .form-section h3 {
            color: #38bdf8;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #e2e8f0;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group textarea {
            width: 100%;
            padding: 12px;
            background-color: #1e293b;
            border: 2px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="number"]:focus,
        .form-group textarea:focus {
            border-color: #38bdf8;
            outline: none;
        }

        .form-group input[type="file"] {
            background-color: #1e293b;
            padding: 10px;
            border-radius: 6px;
            border: 2px dashed #475569;
            width: 100%;
            cursor: pointer;
        }

        .help-text {
            font-size: 0.85rem;
            color: #94a3b8;
            margin-top: 5px;
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            background-color: #1e293b;
            padding: 10px;
            border-radius: 6px;
        }

        .amenity-item input[type="checkbox"] {
            margin-right: 8px;
            width: 18px;
            height: 18px;
            accent-color: #38bdf8;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .submit-btn {
            background-color: #0ea5e9;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .submit-btn:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }

        .cancel-btn {
            background-color: #475569;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
        }

        .cancel-btn:hover {
            background-color: #64748b;
        }

        .success-message {
            background-color: #065f46;
            color: #d1fae5;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .error-message {
            background-color: #991b1b;
            color: #fee2e2;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .view-link {
            background-color: #059669;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .view-link:hover {
            background-color: #047857;
        }

        .current-image {
            margin-bottom: 15px;
        }

        .current-image img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 6px;
            border: 2px solid #475569;
        }

        @media (max-width: 768px) {
            .amenities-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="container">
        <h2 class="text-center text-3xl mb-6 text-cyan-400">Edit Hotel</h2>

        <?php if (!empty($success_message)): ?>
            <?php echo $success_message; ?>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <?php echo $error_message; ?>
        <?php endif; ?>

        <form action="" method="POST" enctype="multipart/form-data" class="hotel-form">
            <div class="form-section">
                <h3>Basic Information</h3>
                <div class="form-group">
                    <label for="name">Hotel Name</label>
                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($hotel['hotel_name']) ?>" required>
                </div>

                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" id="location" name="location" value="<?= htmlspecialchars($hotel['location']) ?>" required>
                </div>

                <div class="form-group">
                    <label for="price_per_night">Price Per Night (₹)</label>
                    <input type="number" id="price_per_night" name="price_per_night" value="<?= htmlspecialchars($hotel['price']) ?>" step="0.01" min="0" required>
                </div>
            </div>

            <div class="form-section">
                <h3>Description</h3>
                <div class="form-group">
                    <label for="description">Hotel Description</label>
                    <textarea id="description" name="description" rows="5" required><?= htmlspecialchars($hotel['description']) ?></textarea>
                </div>
                <div class="form-group">
                    <label for="contact">Contact Information</label>
                    <input type="text" id="contact" name="contact" value="<?= htmlspecialchars($hotel['contact'] ?? '') ?>" placeholder="Phone number, email, or other contact info" maxlength="200">
                    <p class="help-text">This will be displayed on your hotel page for guests to contact you.</p>
                </div>
            </div>

            <div class="form-section">
                <h3>Amenities</h3>
                <div class="amenities-grid">
                    <div class="amenity-item">
                        <input type="checkbox" id="wifi" name="amenities[]" value="WiFi" <?= in_array('WiFi', $amenities) ? 'checked' : '' ?>>
                        <label for="wifi">WiFi</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="parking" name="amenities[]" value="Parking" <?= in_array('Parking', $amenities) ? 'checked' : '' ?>>
                        <label for="parking">Parking</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="pool" name="amenities[]" value="Swimming Pool" <?= in_array('Swimming Pool', $amenities) ? 'checked' : '' ?>>
                        <label for="pool">Swimming Pool</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="ac" name="amenities[]" value="Air Conditioning" <?= in_array('Air Conditioning', $amenities) ? 'checked' : '' ?>>
                        <label for="ac">Air Conditioning</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="restaurant" name="amenities[]" value="Restaurant" <?= in_array('Restaurant', $amenities) ? 'checked' : '' ?>>
                        <label for="restaurant">Restaurant</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="gym" name="amenities[]" value="Gym" <?= in_array('Gym', $amenities) ? 'checked' : '' ?>>
                        <label for="gym">Gym</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="spa" name="amenities[]" value="Spa" <?= in_array('Spa', $amenities) ? 'checked' : '' ?>>
                        <label for="spa">Spa</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="room_service" name="amenities[]" value="Room Service" <?= in_array('Room Service', $amenities) ? 'checked' : '' ?>>
                        <label for="room_service">Room Service</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>Images</h3>
                <div class="form-group">
                    <label>Current Image</label>
                    <div class="current-image">
                        <img src="<?= htmlspecialchars(getImagePath($hotel['img_path'] ?? '')) ?>" alt="<?= htmlspecialchars($hotel['hotel_name']) ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="main_image">Change Main Image (Optional)</label>
                    <input type="file" id="main_image" name="main_image" accept="image/*">
                    <p class="help-text">Leave empty to keep the current image.</p>
                </div>
            </div>

            <div class="form-actions">
                <input type="submit" name="update_hotel" value="Update Hotel" class="submit-btn">
                <a href="profile.php" class="cancel-btn">Cancel</a>
            </div>
        </form>
    </div>

    <!-- Include Cookie Consent Banner -->
    <?php include 'cookie_consent.php'; ?>

    <footer>
        <p>&copy; <?= date('Y') ?> Hotel Finder. Elevate Your Stay.</p>
    </footer>
</body>
</html>
