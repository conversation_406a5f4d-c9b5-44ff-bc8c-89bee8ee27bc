<?php
session_start();
require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/utils.php';

$search_term = isset($_GET['search']) ? $_GET['search'] : '';
$location = isset($_GET['location']) ? $_GET['location'] : '';
$min_price = isset($_GET['min_price']) ? $_GET['min_price'] : '';
$max_price = isset($_GET['max_price']) ? $_GET['max_price'] : '';

// Build the query based on search parameters
$query = "SELECT * FROM hotels WHERE 1=1";
$params = [];

if (!empty($search_term)) {
    $query .= " AND (hotel_name LIKE ? OR description LIKE ?)";
    $params[] = "%$search_term%";
    $params[] = "%$search_term%";
}

if (!empty($location)) {
    $query .= " AND location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($min_price)) {
    $query .= " AND price >= ?";
    $params[] = $min_price;
}

if (!empty($max_price)) {
    $query .= " AND price <= ?";
    $params[] = $max_price;
}

$query .= " ORDER BY id DESC";

// Execute the search query
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $hotels = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    $hotels = [];
}

// Get all unique locations for the dropdown
try {
    $locations = $pdo->query("SELECT DISTINCT location FROM hotels ORDER BY location")->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    $locations = [];
}

// getImagePath function is now in utils.php
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Hotels - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .search-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 0 20px;
        }
        .search-form {
            background-color: #1a1a1a;
            border-radius: 15px;
            box-shadow: 0px 8px 25px rgba(0, 255, 255, 0.2);
            padding: 20px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .search-form input, .search-form select {
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #00e5ff;
            background-color: #0d0d0d;
            color: #e5e5e5;
        }
        .search-form button {
            background-color: #00e5ff;
            color: #0d0d0d;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .search-form button:hover {
            background-color: #0099cc;
        }
        .hotel-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .hotel-card {
            background-color: #1a1a1a;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s;
            cursor: pointer;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .hotel-card:hover {
            transform: translateY(-5px);
            box-shadow: 0px 8px 25px rgba(0, 255, 255, 0.3);
        }
        .hotel-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .hotel-info {
            padding: 15px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        .hotel-name {
            color: #00e5ff;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        .hotel-location {
            color: #aaa;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        .hotel-price {
            color: #00e5ff;
            font-weight: bold;
            margin-top: auto;
        }
        .no-results {
            text-align: center;
            padding: 50px;
            color: #aaa;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="search-container">
        <h2 class="text-center text-3xl mb-8 text-cyan-400">Find Your Perfect Stay</h2>

        <form class="search-form" method="GET">
            <div>
                <input type="text" name="search" placeholder="Search by name or description" value="<?= htmlspecialchars($search_term) ?>">
            </div>
            <div>
                <select name="location">
                    <option value="">All Locations</option>
                    <?php foreach ($locations as $loc): ?>
                        <option value="<?= htmlspecialchars($loc) ?>" <?= $location === $loc ? 'selected' : '' ?>>
                            <?= htmlspecialchars($loc) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <input type="number" name="min_price" placeholder="Min Price" value="<?= htmlspecialchars($min_price) ?>">
            </div>
            <div>
                <input type="number" name="max_price" placeholder="Max Price" value="<?= htmlspecialchars($max_price) ?>">
            </div>
            <div>
                <button type="submit">Search</button>
            </div>
        </form>

        <?php if (empty($hotels)): ?>
            <div class="no-results">
                <p>No hotels found matching your criteria.</p>
                <p>Try adjusting your search parameters.</p>
            </div>
        <?php else: ?>
            <div class="hotel-grid">
                <?php foreach ($hotels as $hotel): ?>
                    <div class="hotel-card" onclick="window.location.href='hotel.php?id=<?= htmlspecialchars($hotel['id']) ?>'">
                        <img src="<?= htmlspecialchars(getImagePath($hotel['image_path'] ?? $hotel['image'] ?? '')) ?>"
                             alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                             loading="lazy">
                        <div class="hotel-info">
                            <h3 class="hotel-name"><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                            <p class="hotel-location"><?= htmlspecialchars($hotel['location']) ?></p>
                            <p class="hotel-price">₹<?= number_format($hotel['price'], 2) ?> / night</p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Include Cookie Consent Banner -->
    <?php include 'cookie_consent.php'; ?>

    <footer>
        <p>&copy; <?= date('Y') ?> Hotel Finder. Elevate Your Stay.</p>
    </footer>
</body>
</html>
