# Hotel Finder Transfer Guide

This guide explains how to transfer your Hotel Finder application to another person.

## What You Need to Transfer

The Hotel Finder application consists of:

1. **PHP Code**: All the PHP files in the `php` directory
2. **Images**: All the hotel images in the `images` directory
3. **Database**: All your hotel and user data stored in the MySQL database

## Step-by-Step Transfer Process

### 1. Export the Database

1. Visit http://localhost/hotel_finder/php/export_database.php in your browser
2. This will download a SQL file containing all your database data
3. Save this file as `hotel_finder_export.sql`

### 2. Package the Application Files

#### Option 1: Create a ZIP Archive (Easiest)

1. Right-click on the `hotel_finder` folder
2. Select "Send to" > "Compressed (zipped) folder"
3. This will create a `hotel_finder.zip` file containing all your code and images

#### Option 2: Manual Copy

1. Copy the entire `hotel_finder` folder
2. Include all PHP files, images, and any other files in the folder

### 3. Send the Files to Your Friend

Send your friend:
1. The `hotel_finder.zip` file (or the entire folder)
2. The `hotel_finder_export.sql` database export file

### 4. Installation Instructions for Your Friend

Your friend will need to:

1. Install XAMPP (or similar web server with PHP and MySQL)
2. Extract the `hotel_finder` folder to their `htdocs` directory
3. Create a new MySQL database called `hotel_finder`
4. Import the `hotel_finder_export.sql` file into their database
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Select the `hotel_finder` database
   - Click the "Import" tab
   - Upload and import the SQL file

5. Update the database connection settings if needed:
   - Edit the `php/db_connect.php` file to match their database settings
   - Default settings are:
     - Host: localhost
     - Database: hotel_finder
     - Username: root
     - Password: (empty)

6. Access the application at http://localhost/hotel_finder/php/index.php

## Troubleshooting

If your friend encounters issues:

1. **Database Connection Error**: Check the `db_connect.php` file and ensure the database credentials are correct
2. **Missing Images**: Make sure all images were transferred in the `images` directory
3. **Permission Issues**: Ensure the web server has read/write permissions for the `images` directory

## Note About User Passwords

When transferring the database, user passwords will be transferred as hashed values (not plain text). This is secure, but it means:

1. Your friend won't know the actual passwords
2. Users can still log in with their original passwords
3. If passwords need to be reset, your friend will need to update them in the database
