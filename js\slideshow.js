document.addEventListener('DOMContentLoaded', function () {
    const slideshows = document.querySelectorAll('.slideshow-container');
    slideshows.forEach(slideshow => {
        let images = slideshow.querySelectorAll('img');
        let index = 0;

        if (images.length > 1) {
            images.forEach(img => img.style.opacity = 0);
            images[0].style.opacity = 1;

            slideshow.addEventListener('mouseenter', () => {
                const interval = setInterval(() => {
                    images[index].style.opacity = 0;
                    index = (index + 1) % images.length;
                    images[index].style.opacity = 1;
                }, 1000);

                slideshow.addEventListener('mouseleave', () => clearInterval(interval));
            });
        }
    });
});
