<?php
// utils.php - Consolidated utility functions for Hotel Finder

// Cookie consent and management functions
function has_cookie_consent() {
    return isset($_COOKIE['cookie_consent']) && $_COOKIE['cookie_consent'] === 'accepted';
}

function set_cookie_consent($consent = 'accepted') {
    setcookie('cookie_consent', $consent, time() + (86400 * 365), '/'); // 1 year
}

function get_recently_viewed_hotels() {
    if (!has_cookie_consent() || !isset($_COOKIE['recently_viewed'])) {
        return [];
    }

    $recently_viewed = json_decode($_COOKIE['recently_viewed'], true);
    return is_array($recently_viewed) ? $recently_viewed : [];
}

function add_recently_viewed_hotel($hotel_id) {
    if (!has_cookie_consent()) {
        return;
    }

    $recently_viewed = get_recently_viewed_hotels();

    // Remove the hotel if it's already in the list
    $recently_viewed = array_diff($recently_viewed, [$hotel_id]);

    // Add the hotel to the beginning of the array
    array_unshift($recently_viewed, $hotel_id);

    // Keep only the 5 most recent hotels
    $recently_viewed = array_slice($recently_viewed, 0, 5);

    // Save the updated list
    setcookie('recently_viewed', json_encode($recently_viewed), time() + (86400 * 30), '/'); // 30 days
}

// Image handling functions
function getImagePath($imagePath) {
    // Default image path - one level up from php folder
    $defaultImage = '../images/default-hotel.jpg';

    // If no image path provided, return default
    if (empty($imagePath)) {
        return $defaultImage;
    }

    // Debug the image path
    error_log("Original image path: " . $imagePath);

    // Clean up the path - remove any double slashes or backslashes
    $imagePath = str_replace('\\', '/', $imagePath);
    $imagePath = str_replace('//', '/', $imagePath);

    // If the path already starts with '../', use it directly
    if (strpos($imagePath, '../') === 0) {
        if (file_exists($imagePath)) {
            return $imagePath;
        }
    }

    // If the path is just a filename or relative path without '../'
    if (strpos($imagePath, '../') !== 0) {
        $fullPath = '../' . ltrim($imagePath, '/');
        if (file_exists($fullPath)) {
            return $fullPath;
        }
    }

    // Try with just the basename
    $basenamePath = '../images/' . basename($imagePath);
    if (file_exists($basenamePath)) {
        return $basenamePath;
    }

    // Try to find the file in the images directory
    $imagesDir = '../images/';
    if (is_dir($imagesDir)) {
        $files = scandir($imagesDir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && stripos($file, basename($imagePath, '.jpg')) !== false) {
                return $imagesDir . $file;
            }
        }
    }

    // Return the default image if all else fails
    return $defaultImage;
}

// Function to create a colored image with text (for default hotel images)
function createHotelImage($width, $height, $text, $bgColor, $textColor, $filename) {
    // Create image
    $image = imagecreatetruecolor($width, $height);

    // Allocate colors
    $bg = imagecolorallocate($image, $bgColor[0], $bgColor[1], $bgColor[2]);
    $txt = imagecolorallocate($image, $textColor[0], $textColor[1], $textColor[2]);

    // Fill background
    imagefill($image, 0, 0, $bg);

    // Add hotel name
    $font = 5; // Built-in font
    $textWidth = imagefontwidth($font) * strlen($text);
    $textHeight = imagefontheight($font);
    $x = intval(($width - $textWidth) / 2);
    $y = intval(($height - $textHeight) / 2);

    imagestring($image, $font, $x, $y, $text, $txt);

    // Save image
    $result = imagejpeg($image, $filename, 90);

    // Free memory
    imagedestroy($image);

    return $result;
}

// Function to generate default images for hotels
function generateDefaultImages($pdo) {
    // Create images directory if it doesn't exist
    $imagesDir = '../images';
    if (!is_dir($imagesDir)) {
        mkdir($imagesDir, 0777, true);
    }

    // Create default image
    $defaultImagePath = "$imagesDir/default-hotel.jpg";
    createHotelImage(800, 600, "Default Hotel Image", [30, 41, 59], [56, 189, 248], $defaultImagePath);

    // Colors for different hotels
    $colors = [
        [142, 68, 173],  // Purple
        [41, 128, 185],   // Blue
        [39, 174, 96],    // Green
        [211, 84, 0],     // Orange
        [22, 160, 133],   // Teal
        [192, 57, 43]     // Red
    ];

    // Get all hotels - use * to get all columns regardless of names
    $stmt = $pdo->query("SELECT * FROM hotels");
    $hotels = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate an image for each hotel
    foreach ($hotels as $index => $hotel) {
        $hotelName = $hotel['hotel_name'];
        $colorIndex = $index % count($colors);
        $bgColor = $colors[$colorIndex];

        // Create a unique filename
        $filename = "$imagesDir/hotel_" . $hotel['id'] . ".jpg";

        // Create the image
        if (createHotelImage(800, 600, $hotelName, $bgColor, [255, 255, 255], $filename)) {
            // Get the column names from the hotels table
            $colStmt = $pdo->query("SHOW COLUMNS FROM hotels");
            $columns = [];
            while ($row = $colStmt->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['Field'];
            }

            $relativePath = 'images/hotel_' . $hotel['id'] . '.jpg';

            // Check which image column exists and update accordingly
            if (in_array('img_path', $columns)) {
                $updateStmt = $pdo->prepare("UPDATE hotels SET img_path = ? WHERE id = ?");
                $updateStmt->execute([$relativePath, $hotel['id']]);
            } elseif (in_array('image', $columns)) {
                $updateStmt = $pdo->prepare("UPDATE hotels SET image = ? WHERE id = ?");
                $updateStmt->execute([$relativePath, $hotel['id']]);
            }
        }
    }

    return count($hotels);
}
?>
