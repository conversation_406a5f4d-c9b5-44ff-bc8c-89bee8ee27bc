<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/db_connect.php';

echo "<h1>Updating Database Structure</h1>";

try {
    // Check if amenities column exists in hotels table
    $stmt = $pdo->query("SHOW COLUMNS FROM hotels LIKE 'amenities'");
    $amenitiesExists = $stmt->rowCount() > 0;
    
    if (!$amenitiesExists) {
        echo "<p>Adding 'amenities' column to hotels table...</p>";
        $pdo->exec("ALTER TABLE hotels ADD COLUMN amenities TEXT DEFAULT NULL");
        echo "<p style='color:green'>✓ Added 'amenities' column successfully!</p>";
    } else {
        echo "<p style='color:green'>✓ 'amenities' column already exists in hotels table.</p>";
    }
    
    // Check if hotel_images table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'hotel_images'");
    $hotelImagesExists = $stmt->rowCount() > 0;
    
    if (!$hotelImagesExists) {
        echo "<p>Creating 'hotel_images' table...</p>";
        $pdo->exec("CREATE TABLE hotel_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hotel_id INT NOT NULL,
            image_path VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (hotel_id) REFERENCES hotels(id) ON DELETE CASCADE
        )");
        echo "<p style='color:green'>✓ Created 'hotel_images' table successfully!</p>";
    } else {
        echo "<p style='color:green'>✓ 'hotel_images' table already exists.</p>";
    }
    
    echo "<p>Database structure update completed successfully!</p>";
    echo "<p><a href='index.php'>Return to Home Page</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Error: " . $e->getMessage() . "</p>";
}
?>
