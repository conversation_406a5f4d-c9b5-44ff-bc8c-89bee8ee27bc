<?php
session_start();
require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/utils.php';

// Check if hotel ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$hotel_id = $_GET['id'];

// Fetch hotel details
try {
    $stmt = $pdo->prepare("SELECT h.*, u.username as owner_name
                          FROM hotels h
                          JOIN users u ON h.owner_id = u.id
                          WHERE h.id = ?");
    $stmt->execute([$hotel_id]);
    $hotel = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$hotel) {
        // Hotel not found
        header("Location: index.php?error=hotel_not_found&id=$hotel_id");
        exit();
    }

    // Debug information
    echo "<!-- Hotel data: " . print_r($hotel, true) . " -->";

    // Map column names if needed
    // Check for image column - could be 'image' or 'image_path'
    if (!isset($hotel['image_path']) && isset($hotel['image'])) {
        $hotel['image_path'] = $hotel['image'];
    } else if (!isset($hotel['image']) && isset($hotel['image_path'])) {
        $hotel['image'] = $hotel['image_path'];
    }

    // Add this hotel to recently viewed list in cookies
    add_recently_viewed_hotel($hotel_id);

    // Get the main image from the hotels table
    $main_image = isset($hotel['image_path']) ? $hotel['image_path'] : (isset($hotel['image']) ? $hotel['image'] : '');

    // Fetch additional images if any
    $stmt = $pdo->prepare("SELECT * FROM hotel_images WHERE hotel_id = ?");
    $stmt->execute([$hotel_id]);
    $additional_images = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug information
    error_log("Hotel ID: $hotel_id - Found " . count($additional_images) . " additional images");

    // Debug all images
    error_log("Main image: $main_image");
    foreach ($additional_images as $index => $image) {
        $img_src = '';
        if (isset($image['image'])) {
            $img_src = $image['image'];
        } else if (isset($image['image_path'])) {
            $img_src = $image['image_path'];
        }
        error_log("Additional image $index: $img_src");
    }

    error_log("After filtering, found " . count($additional_images) . " unique additional images");

    // Debug information
    error_log("Hotel ID: $hotel_id - Found " . count($additional_images) . " additional images");
    foreach ($additional_images as $index => $image) {
        error_log("Additional image $index: " . print_r($image, true));
    }

} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    header("Location: index.php");
    exit();
}

// getImagePath function is now in utils.php

// Check if the hotel has an image, if not, generate one
// Try both possible image column names
if ((empty($hotel['image_path']) || $hotel['image_path'] === null) && (empty($hotel['image']) || $hotel['image'] === null)) {
    // Create a default image for this hotel
    $imagesDir = '../images';
    if (!is_dir($imagesDir)) {
        mkdir($imagesDir, 0777, true);
    }

    $filename = "$imagesDir/hotel_" . $hotel_id . ".jpg";
    $hotelName = $hotel['hotel_name'];

    // Create the image with a blue background
    if (createHotelImage(800, 600, $hotelName, [41, 128, 185], [255, 255, 255], $filename)) {
        // Get the column names from the hotels table
        $stmt = $pdo->query("SHOW COLUMNS FROM hotels");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }

        $relativePath = 'images/hotel_' . $hotel_id . '.jpg';

        // Check which image column exists and update accordingly
        if (in_array('image_path', $columns)) {
            $updateStmt = $pdo->prepare("UPDATE hotels SET image_path = ? WHERE id = ?");
            $updateStmt->execute([$relativePath, $hotel_id]);
            $hotel['image_path'] = $relativePath;
        } elseif (in_array('image', $columns)) {
            $updateStmt = $pdo->prepare("UPDATE hotels SET image = ? WHERE id = ?");
            $updateStmt->execute([$relativePath, $hotel_id]);
            $hotel['image'] = $relativePath;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($hotel['hotel_name']) ?> - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        /* Hotel detail page styling */
        .hotel-container {
            max-width: 1000px;
            margin: 50px auto;
            background-color: #1e293b;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            padding: 30px;
            color: #e2e8f0;
        }

        .hotel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #38bdf8;
            padding-bottom: 15px;
        }

        .hotel-name {
            color: #38bdf8;
            font-size: 2.5rem;
            margin: 0;
            font-weight: 700;
        }

        .hotel-price {
            font-size: 1.5rem;
            color: #38bdf8;
            font-weight: bold;
            background-color: #334155;
            padding: 10px 15px;
            border-radius: 8px;
        }

        .hotel-location {
            font-size: 1.2rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            color: #cbd5e1;
        }

        .hotel-description {
            margin: 25px 0;
            line-height: 1.8;
            color: #e2e8f0;
            background-color: #334155;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #38bdf8;
        }

        .hotel-images {
            margin-top: 25px;
        }

        .images-section-title {
            color: #38bdf8;
            font-size: 1.5rem;
            margin: 20px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #475569;
        }

        .hotel-images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .hotel-image-container {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            height: 180px;
            max-width: 300px;
            margin: 0 auto;
        }

        .hotel-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.4s ease;
        }

        .hotel-image:hover {
            transform: scale(1.08);
            filter: brightness(1.1);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .owner-info {
            margin-top: 30px;
            padding: 15px;
            border-top: 1px solid #475569;
            font-style: italic;
            color: #94a3b8;
        }

        .contact-info {
            margin-top: 8px;
            font-style: normal;
            color: #cbd5e1;
        }

        .contact-info strong {
            color: #38bdf8;
        }

        .book-btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: bold;
            margin-top: 20px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .book-btn:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }

        /* Amenities styling */
        .hotel-amenities {
            margin: 25px 0;
            background-color: #334155;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #38bdf8;
        }

        .amenities-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .amenity-tag {
            background-color: #1e293b;
            color: #38bdf8;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
        }

        .amenity-tag:before {
            content: '✓';
            margin-right: 5px;
            color: #10b981;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="hotel-container">
        <div class="hotel-header">
            <h1 class="hotel-name"><?= htmlspecialchars($hotel['hotel_name']) ?></h1>
            <div class="hotel-price">₹<?= number_format($hotel['price'], 2) ?> / night</div>
        </div>

        <div class="hotel-location">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <?= htmlspecialchars($hotel['location']) ?>
        </div>

        <div class="hotel-images">
            <h2 class="images-section-title">Photos of <?= htmlspecialchars($hotel['hotel_name']) ?></h2>
            <!-- Simple Grid Layout for All Hotel Images -->
            <div class="hotel-images-grid">
                <?php
                // Get all images including the main image
                $all_images = $additional_images;

                // Add the main image to the beginning of the array if it's not already included
                $main_image = isset($hotel['image_path']) ? $hotel['image_path'] : (isset($hotel['image']) ? $hotel['image'] : '');

                // Display all images
                $displayed_images = [];

                // First display the main image if it exists
                if (!empty($main_image)):
                    $displayed_images[] = $main_image;
                ?>
                <div class="hotel-image-container">
                    <img src="<?= htmlspecialchars(getImagePath($main_image)) ?>"
                         alt="<?= htmlspecialchars($hotel['hotel_name']) ?>" class="hotel-image">
                </div>
                <?php endif; ?>

                <!-- Additional Images from hotel_images table -->
                <?php
                // Display additional images from the hotel_images table
                foreach ($additional_images as $image):
                    // Check which column exists in the image data
                    $img_src = '';
                    if (isset($image['image'])) {
                        $img_src = $image['image'];
                    } else if (isset($image['image_path'])) {
                        $img_src = $image['image_path'];
                    }

                    // Only display if we have a valid image source and it's not already displayed
                    if (!empty($img_src) && !in_array($img_src, $displayed_images)):
                        $displayed_images[] = $img_src;
                ?>
                   <div class="hotel-image-container">
                        <img src="<?= htmlspecialchars(getImagePath($img_src)) ?>"
                             alt="Photo of <?= htmlspecialchars($hotel['hotel_name']) ?>"
                             class="hotel-image">
                    </div>
                <?php
                    endif;
                endforeach;
                ?>
            </div>
        </div>

        <div class="hotel-description">
            <h2 class="text-xl text-cyan-400 mb-2">About this hotel</h2>
            <p><?= nl2br(htmlspecialchars($hotel['description'])) ?></p>
        </div>

        <?php if (!empty($hotel['amenities'])): ?>
        <div class="hotel-amenities">
            <h2 class="text-xl text-cyan-400 mb-2">Amenities</h2>
            <div class="amenities-list">
                <?php
                $amenities = explode(',', $hotel['amenities']);
                foreach ($amenities as $amenity):
                ?>
                    <div class="amenity-tag"><?= htmlspecialchars($amenity) ?></div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="owner-info">
            Listed by: <?= htmlspecialchars($hotel['owner_name']) ?>
            <?php if (!empty($hotel['contact'])): ?>
            <div class="contact-info">
                <strong>Contact:</strong> <?= htmlspecialchars($hotel['contact']) ?>
            </div>
            <?php endif; ?>
        </div>

        <?php if (isset($_SESSION['user_id']) && $_SESSION['user_type'] === 'user'): ?>
            <a href="#" class="book-btn">Book Now</a>
        <?php elseif (!isset($_SESSION['user_id'])): ?>
            <p class="mt-4">Please <a href="login.php" class="text-cyan-400">login</a> to book this hotel.</p>
        <?php endif; ?>
    </div>

    <!-- Include Cookie Consent Banner -->
    <?php include 'cookie_consent.php'; ?>

    <footer>
        <p>&copy; <?= date('Y') ?> Hotel Finder. Elevate Your Stay.</p>
    </footer>
</body>
</html>
