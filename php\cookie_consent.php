<?php
require_once __DIR__ . '/utils.php';

// Check if consent has already been given
$consent_given = has_cookie_consent();
?>

<?php if (!$consent_given): ?>
<div id="cookie-consent-banner" class="cookie-banner">
    <div class="cookie-content">
        <h3>Cookie Notice</h3>
        <p>We use cookies to enhance your experience on our website. By continuing to browse, you agree to our use of cookies.</p>
        <div class="cookie-buttons">
            <button id="accept-cookies" class="accept-btn">Accept All Cookies</button>
            <button id="customize-cookies" class="customize-btn">Customize Settings</button>
        </div>
    </div>
</div>

<div id="cookie-settings" class="cookie-settings">
    <div class="cookie-settings-content">
        <h3>Cookie Settings</h3>
        <div class="cookie-option">
            <label>
                <input type="checkbox" id="essential-cookies" checked disabled>
                <span>Essential Cookies</span>
            </label>
            <p>These cookies are necessary for the website to function and cannot be switched off.</p>
        </div>
        <div class="cookie-option">
            <label>
                <input type="checkbox" id="functional-cookies" checked>
                <span>Functional Cookies</span>
            </label>
            <p>These cookies enable personalized features like remembering your preferences.</p>
        </div>
        <div class="cookie-option">
            <label>
                <input type="checkbox" id="analytics-cookies" checked>
                <span>Analytics Cookies</span>
            </label>
            <p>These cookies help us improve our website by collecting anonymous information.</p>
        </div>
        <div class="cookie-settings-buttons">
            <button id="save-preferences" class="save-btn">Save Preferences</button>
            <button id="accept-all-settings" class="accept-btn">Accept All</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const consentBanner = document.getElementById('cookie-consent-banner');
    const settingsPanel = document.getElementById('cookie-settings');
    const acceptBtn = document.getElementById('accept-cookies');
    const customizeBtn = document.getElementById('customize-cookies');
    const savePreferencesBtn = document.getElementById('save-preferences');
    const acceptAllSettingsBtn = document.getElementById('accept-all-settings');

    // Show the consent banner
    consentBanner.style.display = 'block';

    // Accept all cookies
    function acceptAllCookies() {
        // Set cookie consent
        document.cookie = "cookie_consent=accepted; max-age=31536000; path=/; SameSite=Lax";

        // Hide the banner and settings
        consentBanner.style.display = 'none';
        settingsPanel.style.display = 'none';

        // Reload to apply cookie settings
        location.reload();
    }

    // Save preferences
    function savePreferences() {
        const functionalEnabled = document.getElementById('functional-cookies').checked;
        const analyticsEnabled = document.getElementById('analytics-cookies').checked;

        // Set cookie consent
        document.cookie = "cookie_consent=accepted; max-age=31536000; path=/; SameSite=Lax";
        document.cookie = "functional_cookies=" + (functionalEnabled ? "1" : "0") + "; max-age=31536000; path=/; SameSite=Lax";
        document.cookie = "analytics_cookies=" + (analyticsEnabled ? "1" : "0") + "; max-age=31536000; path=/; SameSite=Lax";

        // Hide the banner and settings
        consentBanner.style.display = 'none';
        settingsPanel.style.display = 'none';

        // Reload to apply cookie settings
        location.reload();
    }

    // Event listeners
    acceptBtn.addEventListener('click', acceptAllCookies);
    acceptAllSettingsBtn.addEventListener('click', acceptAllCookies);

    customizeBtn.addEventListener('click', function() {
        consentBanner.style.display = 'none';
        settingsPanel.style.display = 'block';
    });

    savePreferencesBtn.addEventListener('click', savePreferences);
});
</script>

<style>
.cookie-banner {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #1e293b;
    color: #e2e8f0;
    padding: 20px;
    box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border-top: 4px solid #38bdf8;
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.cookie-content h3 {
    color: #38bdf8;
    margin-bottom: 12px;
    font-size: 1.3rem;
    font-weight: 600;
}

.cookie-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.accept-btn, .customize-btn, .save-btn {
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.accept-btn {
    background-color: #0ea5e9;
    color: white;
}

.accept-btn:hover {
    background-color: #0284c7;
    transform: translateY(-2px);
}

.customize-btn, .save-btn {
    background-color: #334155;
    color: #e2e8f0;
    border: 1px solid #475569;
}

.customize-btn:hover, .save-btn:hover {
    background-color: #475569;
    transform: translateY(-2px);
}

.cookie-settings {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.9);
    z-index: 1001;
    overflow-y: auto;
}

.cookie-settings-content {
    max-width: 600px;
    margin: 50px auto;
    background-color: #1e293b;
    border-radius: 12px;
    padding: 25px;
    color: #e2e8f0;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border-left: 4px solid #38bdf8;
}

.cookie-settings-content h3 {
    color: #38bdf8;
    margin-bottom: 25px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
}

.cookie-option {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #475569;
    background-color: #334155;
    padding: 15px;
    border-radius: 8px;
}

.cookie-option label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #38bdf8;
}

.cookie-option input {
    margin-right: 12px;
    width: 18px;
    height: 18px;
    accent-color: #38bdf8;
}

.cookie-option p {
    margin-left: 30px;
    font-size: 0.95rem;
    color: #cbd5e1;
    line-height: 1.5;
}

.cookie-settings-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 25px;
}

@media (max-width: 768px) {
    .cookie-buttons,
    .cookie-settings-buttons {
        flex-direction: column;
        width: 100%;
    }

    .accept-btn, .customize-btn, .save-btn {
        width: 100%;
        padding: 12px;
    }

    .cookie-settings-content {
        margin: 20px;
        width: auto;
    }
}
</style>
<?php endif; ?>
