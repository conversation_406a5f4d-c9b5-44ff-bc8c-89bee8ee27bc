<?php
// cookie_utils.php - Utility functions for handling cookies securely

/**
 * Set a secure cookie with customizable options
 * 
 * @param string $name Cookie name
 * @param string $value Cookie value
 * @param array $options Additional cookie options
 * @return bool True on success, false on failure
 */
function set_secure_cookie($name, $value, $options = []) {
    // Default cookie options
    $default_options = [
        'expires' => 0,           // Session cookie by default
        'path' => '/',            // Available across the entire domain
        'domain' => '',           // Current domain
        'secure' => true,         // Only sent over HTTPS
        'httponly' => true,       // Not accessible via JavaScript
        'samesite' => 'Lax'       // Lax same-site policy
    ];
    
    // Merge with user options
    $options = array_merge($default_options, $options);
    
    // Set the cookie
    return setcookie(
        $name,
        $value,
        [
            'expires' => $options['expires'],
            'path' => $options['path'],
            'domain' => $options['domain'],
            'secure' => $options['secure'],
            'httponly' => $options['httponly'],
            'samesite' => $options['samesite']
        ]
    );
}

/**
 * Get a cookie value
 * 
 * @param string $name Cookie name
 * @param mixed $default Default value if cookie doesn't exist
 * @return mixed Cookie value or default
 */
function get_cookie($name, $default = null) {
    return isset($_COOKIE[$name]) ? $_COOKIE[$name] : $default;
}

/**
 * Delete a cookie
 * 
 * @param string $name Cookie name
 * @return bool True on success, false on failure
 */
function delete_cookie($name) {
    // Set expiration to the past to delete the cookie
    return setcookie($name, '', time() - 3600, '/');
}

/**
 * Check if cookies are enabled in the browser
 * 
 * @return bool True if cookies are enabled, false otherwise
 */
function are_cookies_enabled() {
    // Try to set a test cookie
    setcookie('test_cookie', '1', time() + 60, '/');
    
    // Check if the cookie was set
    return isset($_COOKIE['test_cookie']);
}
?>
