<?php
session_start();
include __DIR__ . '/db_connect.php'; // Ensure correct DB connection path
require_once __DIR__ . '/utils.php';

// Check if the user is logged in and is an owner
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'owner') {
    header("Location: login.php");
    exit();
}

// Get owner information
try {
    $stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $owner = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $owner = ['name' => 'Hotel Owner'];
}

// Success message
$success_message = '';
$error_message = '';

// Handle hotel submission
if (isset($_POST['add_hotel'])) {
    // Get form data
    $name = $_POST['name'];
    $location = $_POST['location'];
    $price = $_POST['price_per_night'];
    $description = $_POST['description'];
    $contact = isset($_POST['contact']) ? $_POST['contact'] : '';
    $owner_id = $_SESSION['user_id'];
    $amenities = isset($_POST['amenities']) ? implode(',', $_POST['amenities']) : '';

    // Handle image uploads
    $upload_dir = '../images/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    // Array to store all image paths
    $hotel_images = [];

    // Process all 3 required images
    $all_images_uploaded = true;
    $main_image_path = '';

    // Check if all required images are provided
    for ($i = 1; $i <= 3; $i++) {
        $image_key = 'image' . $i;

        if (!isset($_FILES[$image_key]) || $_FILES[$image_key]['error'] != 0 || empty($_FILES[$image_key]['name'])) {
            $all_images_uploaded = false;
            $error_message = "<div class='error-message'>Please upload all 3 required hotel images.</div>";
            break;
        }
    }

    // If all images are provided, process them
    if ($all_images_uploaded) {
        for ($i = 1; $i <= 3; $i++) {
            $image_key = 'image' . $i;

            // Clean the filename to remove special characters
            $original_name = basename($_FILES[$image_key]['name']);
            $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);

            // Create a unique filename
            $sanitized_name = preg_replace('/[^a-zA-Z0-9_]/', '', $name); // Remove special chars from hotel name
            $image_name = time() . '_' . $i . '_' . $sanitized_name . '.' . $file_extension;
            $target_path = $upload_dir . $image_name;

            if (move_uploaded_file($_FILES[$image_key]['tmp_name'], $target_path)) {
                $image_path = 'images/' . $image_name;
                // Make sure the path is correctly formatted
                $image_path = str_replace('\\', '/', $image_path);
                $image_path = str_replace('//', '/', $image_path);

                $hotel_images[] = $image_path;

                // Store the first image as main image for database compatibility
                if ($i == 1) {
                    $main_image_path = $image_path;
                }
            } else {
                $error_message = "<div class='error-message'>Error uploading image {$i}. Please try again.</div>";
                $all_images_uploaded = false;
                break;
            }
        }
    }

    if (empty($error_message)) {
        try {
            // Get the column names from the hotels table
            $stmt = $pdo->query("SHOW COLUMNS FROM hotels");
            $columns = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $row['Field'];
            }

            // Build the SQL query dynamically
            $data = [
                'id' => null, // Auto-increment
                'owner_id' => $owner_id,
                'hotel_name' => $name,
                'location' => $location,
                'price' => $price,
                'description' => $description,
                'contact' => $contact
            ];

            // Add the main image to the hotels table
            if (!empty($main_image_path)) {
                // Check which column exists in the hotels table
                if (in_array('image', $columns)) {
                    $data['image'] = $main_image_path;
                } else if (in_array('img', $columns)) {
                    $data['img'] = $main_image_path;
                } else if (in_array('image_path', $columns)) {
                    $data['image_path'] = $main_image_path;
                } else if (in_array('img_path', $columns)) {
                    $data['img_path'] = $main_image_path;
                }
            }

            // Add amenities if available
            if (in_array('amenities', $columns) && !empty($amenities)) {
                $data['amenities'] = $amenities;
            }

            // Remove any keys that don't exist in the columns
            foreach (array_keys($data) as $key) {
                if (!in_array($key, $columns)) {
                    unset($data[$key]);
                }
            }

            // Build the SQL query
            $fields = array_keys($data);
            $placeholders = array_fill(0, count($fields), '?');

            $sql = "INSERT INTO hotels (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";

            // Begin transaction
            $pdo->beginTransaction();

            // Execute the query
            $stmt = $pdo->prepare($sql);
            $stmt->execute(array_values($data));

            // Get the hotel ID
            $hotel_id = $pdo->lastInsertId();

            // Save all images to hotel_images table
            // Save all images including the first one
            foreach ($hotel_images as $index => $image_path) {
                try {
                    // Check which column exists in the hotel_images table
                    $stmt = $pdo->query("SHOW COLUMNS FROM hotel_images LIKE 'image_path'");
                    $image_path_exists = $stmt->rowCount() > 0;

                    $stmt = $pdo->query("SHOW COLUMNS FROM hotel_images LIKE 'image'");
                    $image_exists = $stmt->rowCount() > 0;

                    // Insert into hotel_images table with the correct column name
                    if ($image_path_exists) {
                        $img_stmt = $pdo->prepare("INSERT INTO hotel_images (hotel_id, image_path) VALUES (?, ?)");
                    } else if ($image_exists) {
                        $img_stmt = $pdo->prepare("INSERT INTO hotel_images (hotel_id, image) VALUES (?, ?)");
                    } else {
                        // Default to image_path if neither column exists
                        $pdo->exec("ALTER TABLE hotel_images ADD COLUMN image_path VARCHAR(255) NOT NULL");
                        $img_stmt = $pdo->prepare("INSERT INTO hotel_images (hotel_id, image_path) VALUES (?, ?)");
                    }

                    $img_stmt->execute([$hotel_id, $image_path]);
                    error_log("Image " . ($index + 1) . " added to database successfully. Path: {$image_path}");
                } catch (PDOException $e) {
                    error_log("Error adding image " . ($index + 1) . " to database: " . $e->getMessage());
                }
            }

            // Commit transaction
            $pdo->commit();

            $success_message = "<div class='success-message'>Hotel added successfully! <a href='hotel.php?id=$hotel_id' class='view-link'>View Hotel</a></div>";
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $error_message = "<div class='error-message'>Error: " . $e->getMessage() . "</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Finder - Add a New Hotel</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        /* Add Hotel Page Styles */
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 30px;
            background-color: #1e293b;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .hotel-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-section {
            background-color: #334155;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #38bdf8;
        }

        .form-section h3 {
            color: #38bdf8;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #e2e8f0;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group textarea {
            width: 100%;
            padding: 12px;
            background-color: #1e293b;
            border: 2px solid #475569;
            border-radius: 6px;
            color: #e2e8f0;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input[type="text"]:focus,
        .form-group input[type="number"]:focus,
        .form-group textarea:focus {
            border-color: #38bdf8;
            outline: none;
        }

        .form-group input[type="file"] {
            background-color: #1e293b;
            padding: 10px;
            border-radius: 6px;
            border: 2px dashed #475569;
            width: 100%;
            cursor: pointer;
        }

        .help-text {
            font-size: 0.85rem;
            color: #94a3b8;
            margin-top: 5px;
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .amenity-item {
            display: flex;
            align-items: center;
            background-color: #1e293b;
            padding: 10px;
            border-radius: 6px;
        }

        .amenity-item input[type="checkbox"] {
            margin-right: 8px;
            width: 18px;
            height: 18px;
            accent-color: #38bdf8;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .submit-btn {
            background-color: #0ea5e9;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .submit-btn:hover {
            background-color: #0284c7;
            transform: translateY(-2px);
        }

        .cancel-btn {
            background-color: #475569;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
        }

        .cancel-btn:hover {
            background-color: #64748b;
        }

        .success-message {
            background-color: #065f46;
            color: #d1fae5;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .error-message {
            background-color: #991b1b;
            color: #fee2e2;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .view-link {
            background-color: #059669;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .view-link:hover {
            background-color: #047857;
        }

        @media (max-width: 768px) {
            .amenities-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Hotel Finder</h1>
        <nav>
            <a href="index.php">Home</a>
            <a href="contact.php">Contact Us</a>
            <a href="profile.php">Profile</a>
            <a href="logout.php">Logout</a>
        </nav>
    </header>

    <div class="container">
        <h2 class="text-center text-3xl mb-6 text-cyan-400">Add a New Hotel</h2>

        <?php if (!empty($success_message)): ?>
            <?php echo $success_message; ?>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <?php echo $error_message; ?>
        <?php endif; ?>

        <form action="" method="POST" enctype="multipart/form-data" class="hotel-form">
            <div class="form-section">
                <h3>Basic Information</h3>
                <div class="form-group">
                    <label for="name">Hotel Name</label>
                    <input type="text" id="name" name="name" placeholder="Enter hotel name" required>
                </div>

                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" id="location" name="location" placeholder="City, State, Country" required>
                </div>

                <div class="form-group">
                    <label for="price_per_night">Price Per Night (₹)</label>
                    <input type="number" id="price_per_night" name="price_per_night" placeholder="Enter price" step="0.01" min="0" required>
                </div>
            </div>

            <div class="form-section">
                <h3>Description</h3>
                <div class="form-group">
                    <label for="description">Hotel Description</label>
                    <textarea id="description" name="description" placeholder="Describe your hotel, its features, and what makes it special" rows="5" required></textarea>
                </div>
                <div class="form-group">
                    <label for="contact">Contact Information</label>
                    <input type="text" id="contact" name="contact" placeholder="Phone number, email, or other contact info" maxlength="200">
                    <p class="help-text">This will be displayed on your hotel page for guests to contact you.</p>
                </div>
            </div>

            <div class="form-section">
                <h3>Amenities</h3>
                <div class="amenities-grid">
                    <div class="amenity-item">
                        <input type="checkbox" id="wifi" name="amenities[]" value="WiFi">
                        <label for="wifi">WiFi</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="parking" name="amenities[]" value="Parking">
                        <label for="parking">Parking</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="pool" name="amenities[]" value="Swimming Pool">
                        <label for="pool">Swimming Pool</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="ac" name="amenities[]" value="Air Conditioning">
                        <label for="ac">Air Conditioning</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="restaurant" name="amenities[]" value="Restaurant">
                        <label for="restaurant">Restaurant</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="gym" name="amenities[]" value="Gym">
                        <label for="gym">Gym</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="spa" name="amenities[]" value="Spa">
                        <label for="spa">Spa</label>
                    </div>
                    <div class="amenity-item">
                        <input type="checkbox" id="room_service" name="amenities[]" value="Room Service">
                        <label for="room_service">Room Service</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>Images</h3>
                <div class="form-group">
                    <label for="image1">Hotel Image 1</label>
                    <input type="file" id="image1" name="image1" accept="image/*" required>
                </div>

                <div class="form-group">
                    <label for="image2">Hotel Image 2</label>
                    <input type="file" id="image2" name="image2" accept="image/*" required>
                </div>

                <div class="form-group">
                    <label for="image3">Hotel Image 3</label>
                    <input type="file" id="image3" name="image3" accept="image/*" required>
                </div>
            </div>

            <div class="form-actions">
                <input type="submit" name="add_hotel" value="Add Hotel" class="submit-btn">
                <a href="index.php" class="cancel-btn">Cancel</a>
            </div>
        </form>
    </div>
</body>
</html>
