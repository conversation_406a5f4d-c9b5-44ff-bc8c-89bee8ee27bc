/**
 * Hotel Finder Index Page Effects
 * Adds interactive effects to hotel thumbnails on the index page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all hotel image grids
    const hotelImageGrids = document.querySelectorAll('.hotel-images-grid');
    
    hotelImageGrids.forEach(grid => {
        const images = grid.querySelectorAll('img');
        
        // Add hover effects to each image
        images.forEach(img => {
            img.addEventListener('mouseenter', function() {
                // Add a subtle rotation to the image
                const randomRotation = (Math.random() * 2 - 1) * 2; // Random value between -2 and 2 degrees
                this.style.transform = `scale(1.05) rotate(${randomRotation}deg)`;
                
                // Change the z-index to bring this image to the front
                this.style.zIndex = '10';
                
                // Add a subtle shadow glow effect
                this.style.boxShadow = '0 0 15px rgba(56, 189, 248, 0.5)';
                
                // Slightly change the filter
                this.style.filter = 'brightness(1.1) contrast(1.1)';
                
                // Apply a subtle effect to other images in the same grid
                images.forEach(otherImg => {
                    if (otherImg !== img) {
                        otherImg.style.filter = 'brightness(0.8) grayscale(0.3)';
                        otherImg.style.transform = 'scale(0.95)';
                    }
                });
            });
            
            // Reset effects on mouse leave
            img.addEventListener('mouseleave', function() {
                // Reset this image
                this.style.transform = '';
                this.style.zIndex = '';
                this.style.boxShadow = '';
                this.style.filter = '';
                
                // Reset other images
                images.forEach(otherImg => {
                    if (otherImg !== img) {
                        otherImg.style.filter = '';
                        otherImg.style.transform = '';
                    }
                });
            });
        });
    });
});
