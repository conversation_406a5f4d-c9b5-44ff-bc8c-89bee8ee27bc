<?php
session_start();
include __DIR__ . '/db_connect.php';  // Ensure correct path for db_connect.php
require_once __DIR__ . '/utils.php';  // For has_cookie_consent function

// Check if cookie_utils.php exists, if not, create it
if (!file_exists(__DIR__ . '/cookie_utils.php')) {
    // Create the cookie_utils.php file with basic cookie functions
    $cookie_utils_content = '<?php
// cookie_utils.php - Utility functions for handling cookies securely

/**
 * Set a secure cookie with customizable options
 *
 * @param string $name Cookie name
 * @param string $value Cookie value
 * @param array $options Additional cookie options
 * @return bool True on success, false on failure
 */
function set_secure_cookie($name, $value, $options = []) {
    // Default cookie options
    $default_options = [
        "expires" => 0,           // Session cookie by default
        "path" => "/",            // Available across the entire domain
        "domain" => "",           // Current domain
        "secure" => true,         // Only sent over HTTPS
        "httponly" => true,       // Not accessible via JavaScript
        "samesite" => "Lax"       // Lax same-site policy
    ];

    // Merge with user options
    $options = array_merge($default_options, $options);

    // Set the cookie
    return setcookie(
        $name,
        $value,
        [
            "expires" => $options["expires"],
            "path" => $options["path"],
            "domain" => $options["domain"],
            "secure" => $options["secure"],
            "httponly" => $options["httponly"],
            "samesite" => $options["samesite"]
        ]
    );
}

/**
 * Get a cookie value
 *
 * @param string $name Cookie name
 * @param mixed $default Default value if cookie doesn\'t exist
 * @return mixed Cookie value or default
 */
function get_cookie($name, $default = null) {
    return isset($_COOKIE[$name]) ? $_COOKIE[$name] : $default;
}

/**
 * Delete a cookie
 *
 * @param string $name Cookie name
 * @return bool True on success, false on failure
 */
function delete_cookie($name) {
    // Set expiration to the past to delete the cookie
    return setcookie($name, "", time() - 3600, "/");
}

/**
 * Check if cookies are enabled in the browser
 *
 * @return bool True if cookies are enabled, false otherwise
 */
function are_cookies_enabled() {
    // Try to set a test cookie
    setcookie("test_cookie", "1", time() + 60, "/");

    // Check if the cookie was set
    return isset($_COOKIE["test_cookie"]);
}
?>';
    file_put_contents(__DIR__ . '/cookie_utils.php', $cookie_utils_content);
}

require_once __DIR__ . '/cookie_utils.php';

// Check if remember me cookie exists
if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_user'])) {
    $remember_token = $_COOKIE['remember_user'];

    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE remember_token = ?");
        $stmt->execute([$remember_token]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            // Auto-login the user
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_type'] = $user['user_type'];
        }
    } catch (PDOException $e) {
        error_log("Error checking remember token: " . $e->getMessage());
    }
}

if (isset($_POST['login'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $remember_me = isset($_POST['remember_me']);

    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_type'] = $user['user_type'];

            // Handle remember me functionality
            if ($remember_me && has_cookie_consent()) {
                // Generate a unique token
                $token = bin2hex(random_bytes(32));

                // Store token in database
                $stmt = $pdo->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                $stmt->execute([$token, $user['id']]);

                // Set cookie to expire in 30 days
                set_secure_cookie('remember_user', $token, ['expires' => time() + (86400 * 30)]);
            }

            header("Location: index.php");
            exit();
        } else {
            $error_message = "Invalid username or password.";
        }
    } catch (PDOException $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Poppins', sans-serif; background-color: #0d0d0d; color: #e5e5e5; }
        header { background-color: #1a1a1a; padding: 20px; box-shadow: 0px 4px 15px rgba(0, 255, 255, 0.3); }
        header h1 { color: #00e5ff; font-size: 2.5rem; text-shadow: 0px 2px 5px rgba(0, 229, 255, 0.6); }
        nav a { margin: 0 15px; color: #e5e5e5; text-decoration: none; font-weight: 500; }
        nav a:hover { color: #00e5ff; }
        .container { max-width: 600px; margin: 50px auto; background-color: #1a1a1a; border-radius: 15px; box-shadow: 0px 8px 25px rgba(0, 255, 255, 0.2); padding: 30px; }
        h2 { color: #00e5ff; margin-bottom: 20px; text-align: center; }
        input { width: 100%; padding: 10px; margin-bottom: 15px; border-radius: 8px; border: 2px solid #00e5ff; background-color: #0d0d0d; color: #e5e5e5; }
        input:focus { outline: none; border-color: #0099cc; }
        input[type="submit"] { background-color: #00e5ff; color: #0d0d0d; font-weight: bold; cursor: pointer; transition: 0.3s; }
        input[type="submit"]:hover { background-color: #0099cc; }
        p { text-align: center; margin-top: 20px; }
        a { color: #00e5ff; text-decoration: none; }
        a:hover { color: #0099cc; }
    </style>
</head>
<body>
    <header>
        <h1>Hotel Finder</h1>
        <nav>
            <a href="contact.php">Contact Us</a>
            <a href="register.php">Register</a>

            <a href="logout.php">Logout</a>
            <a href="index.php">Home</a>
        </nav>
    </header>

    <div class="container">
        <h2>Login</h2>
        <?php if (isset($error_message)) { echo "<p style='color: red; text-align:center;'>$error_message</p>"; } ?>
        <form method="POST">
            <input type="text" name="username" placeholder="Username" required>
            <input type="password" name="password" placeholder="Password" required>
            <div class="flex items-center mb-4">
                <input type="checkbox" name="remember_me" id="remember_me" class="w-auto mr-2">
                <label for="remember_me" class="text-sm">Remember me</label>
            </div>
            <input type="submit" name="login" value="Login">
            <p class="mt-4 text-sm">Don't have an account? <a href="register.php">Register here</a></p>
        </form>
    </div>

    <!-- Include Cookie Consent Banner -->
    <?php include 'cookie_consent.php'; ?>

</body>
</html>
