<?php
// Include database connection
require_once __DIR__ . '/db_connect.php';

// Set headers for file download
header('Content-Type: text/plain');
header('Content-Disposition: attachment; filename="hotel_finder_export.sql"');

// Get all tables
$tables = [];
$result = $pdo->query("SHOW TABLES");
while ($row = $result->fetch(PDO::FETCH_NUM)) {
    $tables[] = $row[0];
}

// Export header
echo "-- Hotel Finder Database Export\n";
echo "-- Generated: " . date('Y-m-d H:i:s') . "\n\n";

// Export each table structure and data
foreach ($tables as $table) {
    // Get create table statement
    $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
    $row = $stmt->fetch(PDO::FETCH_NUM);
    $createTable = $row[1];
    
    echo "-- Table structure for table `$table`\n";
    echo "DROP TABLE IF EXISTS `$table`;\n";
    echo $createTable . ";\n\n";
    
    // Get table data
    $result = $pdo->query("SELECT * FROM `$table`");
    $rows = $result->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($rows) > 0) {
        echo "-- Data for table `$table`\n";
        
        // Get column names
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';
        
        // Start insert statement
        echo "INSERT INTO `$table` ($columnList) VALUES\n";
        
        // Add each row
        $rowCount = count($rows);
        foreach ($rows as $i => $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = $pdo->quote($value);
                }
            }
            
            echo "(" . implode(', ', $values) . ")";
            
            // Add comma if not the last row
            if ($i < $rowCount - 1) {
                echo ",\n";
            } else {
                echo ";\n\n";
            }
        }
    }
}

// Export footer
echo "-- End of export\n";
?>
