# Enable URL rewriting
RewriteEngine On

# Redirect non-www to www (Optional for SEO purposes)
RewriteCond %{HTTP_HOST} ^hotel_finder\.local$ [NC]
RewriteRule ^(.*)$ http://www.hotel_finder.local/$1 [L,R=301]

# Allow direct access to actual files and directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Handle static assets
RewriteRule ^(images|css|js)/ - [L]

# Protect sensitive files
<FilesMatch "^(db_connect\.php)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Error handling
ErrorDocument 404 /404.php
ErrorDocument 500 /500.php
