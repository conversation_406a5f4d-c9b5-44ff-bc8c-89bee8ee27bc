<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// php/db_connect.php
$host = 'localhost';
$dbname = 'hotel_db';  // Updated to match your actual database name
$username = 'root';
$password = '';  // Default XAMPP password is usually blank

try {
    // Simply connect to the existing database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if we can connect to the hotels table
    $stmt = $pdo->query("SELECT COUNT(*) FROM hotels");
    $hotelCount = $stmt->fetchColumn();

    echo "<!-- Connected to database: $dbname, found $hotelCount hotels -->";

    // Check if remember_token column exists in users table
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'remember_token'");
    if ($stmt->rowCount() == 0) {
        // Add remember_token column if it doesn't exist
        $pdo->exec("ALTER TABLE users ADD COLUMN remember_token VARCHAR(255) DEFAULT NULL");
        echo "<!-- Added remember_token column to users table -->";
    }
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
