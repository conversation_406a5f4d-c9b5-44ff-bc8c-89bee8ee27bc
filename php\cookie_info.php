<?php
// cookie_info.php - Information about cookies used on the site

// Include necessary files
require_once __DIR__ . '/utils.php';
require_once __DIR__ . '/cookie_utils.php';

// Define cookie information
$cookie_info = [
    'essential' => [
        'name' => 'Essential Cookies',
        'description' => 'These cookies are necessary for the website to function and cannot be switched off. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in or filling in forms.',
        'examples' => ['PHPSESSID', 'cookie_consent']
    ],
    'functional' => [
        'name' => 'Functional Cookies',
        'description' => 'These cookies enable the website to provide enhanced functionality and personalization. They may be set by us or by third party providers whose services we have added to our pages.',
        'examples' => ['remember_user', 'recently_viewed']
    ],
    'analytics' => [
        'name' => 'Analytics Cookies',
        'description' => 'These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site.',
        'examples' => ['_ga', '_gid']
    ]
];

/**
 * Get information about all cookies
 * 
 * @return array Cookie information
 */
function get_all_cookie_info() {
    global $cookie_info;
    return $cookie_info;
}

/**
 * Get information about a specific cookie type
 * 
 * @param string $type Cookie type (essential, functional, analytics)
 * @return array|null Cookie information or null if type not found
 */
function get_cookie_info($type) {
    global $cookie_info;
    return isset($cookie_info[$type]) ? $cookie_info[$type] : null;
}

/**
 * Check if a specific cookie type is enabled
 * 
 * @param string $type Cookie type (essential, functional, analytics)
 * @return bool True if enabled, false otherwise
 */
function is_cookie_type_enabled($type) {
    // Essential cookies are always enabled
    if ($type === 'essential') {
        return true;
    }
    
    // Check if consent has been given
    if (!has_cookie_consent()) {
        return false;
    }
    
    // Check specific cookie type
    switch ($type) {
        case 'functional':
            return isset($_COOKIE['functional_cookies']) && $_COOKIE['functional_cookies'] === '1';
        case 'analytics':
            return isset($_COOKIE['analytics_cookies']) && $_COOKIE['analytics_cookies'] === '1';
        default:
            return false;
    }
}
?>
