<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/utils.php';

// Create default images directory and default image if they don't exist
$imagesDir = '../images';
if (!is_dir($imagesDir)) {
    mkdir($imagesDir, 0777, true);
}

// Create default image if it doesn't exist
$defaultImage = "$imagesDir/default-hotel.jpg";
if (!file_exists($defaultImage)) {
    createHotelImage(800, 600, "Default Hotel Image", [30, 41, 59], [56, 189, 248], $defaultImage);
}

// Check if we need to generate hotel images
$message = '';
if (isset($_GET['generate_images']) && $_GET['generate_images'] == 1) {
    try {
        $count = generateDefaultImages($pdo);
        $message = "<div class='bg-green-800 text-white p-4 rounded-lg mb-4'>Successfully generated images for $count hotels!</div>";
    } catch (Exception $e) {
        $message = "<div class='bg-red-800 text-white p-4 rounded-lg mb-4'>Error generating images: " . $e->getMessage() . "</div>";
    }
}

// Get search parameters if any
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// Build the query based on search parameters
if (!empty($search_term)) {
    $query = "SELECT * FROM hotels WHERE hotel_name LIKE ? OR location LIKE ? OR description LIKE ? ORDER BY id DESC";
    $params = ["%$search_term%", "%$search_term%", "%$search_term%"];
} else {
    $query = "SELECT * FROM hotels ORDER BY id DESC";
    $params = [];
}

// Add error handling for database query
try {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $hotels = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch all images for each hotel
    foreach ($hotels as $key => $hotel) {
        // Get additional images
        $img_stmt = $pdo->prepare("SELECT * FROM hotel_images WHERE hotel_id = ?");
        $img_stmt->execute([$hotel['id']]);
        $hotels[$key]['additional_images'] = $img_stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    $hotels = [];
}

// Get all unique locations for the dropdown
try {
    $locations = $pdo->query("SELECT DISTINCT location FROM hotels ORDER BY location")->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    error_log("Database Error: " . $e->getMessage());
    $locations = [];
}

// Get recently viewed hotels if any
$recently_viewed = [];
if (has_cookie_consent()) {
    $recent_ids = get_recently_viewed_hotels();
    if (!empty($recent_ids)) {
        try {
            $placeholders = implode(',', array_fill(0, count($recent_ids), '?'));
            $stmt = $pdo->prepare("SELECT * FROM hotels WHERE id IN ($placeholders) ORDER BY FIELD(id, $placeholders)");
            $stmt->execute(array_merge($recent_ids, $recent_ids));
            $recently_viewed = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
        }
    }
}

// getImagePath function is now in utils.php
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <script src="../js/slideshow.js" defer></script>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="container">
        <?php if (!empty($message)) echo $message; ?>

        <?php if (isset($_GET['error']) && $_GET['error'] == 'hotel_not_found'): ?>
            <div class="bg-red-800 text-white p-4 rounded-lg mb-4">
                Hotel with ID <?= htmlspecialchars($_GET['id'] ?? 'Unknown') ?> was not found or has been deleted.
            </div>
        <?php endif; ?>

        <h2 class="text-center text-3xl mb-8 text-cyan-400">Discover Your Ideal Stay!</h2>

        <!-- Quick Search Form -->
        <div class="search-box mb-8">
            <form action="" method="GET" class="flex flex-col md:flex-row gap-2 justify-center">
                <input type="text" name="search" placeholder="Search by name, location or description"
                       value="<?= htmlspecialchars($search_term) ?>"
                       class="flex-grow px-4 py-2 rounded-lg border-2 border-cyan-400 bg-gray-900 text-white">
                <button type="submit" class="px-6 py-2 bg-cyan-400 text-gray-900 font-bold rounded-lg hover:bg-cyan-500 transition-colors">
                    Search
                </button>
                <a href="search.php" class="px-4 py-2 text-cyan-400 border border-cyan-400 rounded-lg text-center hover:bg-gray-800 transition-colors">
                    Advanced Search
                </a>
            </form>
        </div>

        <?php if (empty($hotels)): ?>
            <p class="text-center text-gray-400">No hotels available at the moment.</p>
            <div class="bg-gray-800 p-4 rounded-lg mb-8">
                <h3 class="text-xl text-cyan-400 mb-2">Debug Information:</h3>
                <p>Query: <?= isset($query) ? htmlspecialchars($query) : 'No query' ?></p>
                <p>Parameters: <?= !empty($params) ? htmlspecialchars(print_r($params, true)) : 'No parameters' ?></p>
                <p>Database Connection: <?= isset($pdo) ? 'Connected' : 'Not connected' ?></p>
                <p><a href="check_database.php" class="text-cyan-400 underline">Check Database</a> | <a href="check_database_structure.php" class="text-cyan-400 underline">Check Database Structure</a></p>
            </div>
        <?php else: ?>
            <div class="hotel-list">
                <?php foreach ($hotels as $hotel): ?>
                    <div class="hotel-card" onclick="window.location.href='hotel.php?id=<?= htmlspecialchars($hotel['id']) ?>'">
                        <div class="hotel-images-grid">
                            <!-- Main image from hotels table -->
                            <img src="<?= htmlspecialchars(getImagePath($hotel['image_path'] ?? $hotel['image'] ?? '')) ?>"
                                 alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                                 class="hotel-thumbnail"
                                 loading="lazy">

                            <!-- Additional images from hotel_images table -->
                            <?php foreach ($hotel['additional_images'] as $image): ?>
                                <?php
                                $img_src = '';
                                if (isset($image['image'])) {
                                    $img_src = $image['image'];
                                } else if (isset($image['image_path'])) {
                                    $img_src = $image['image_path'];
                                }
                                ?>
                                <img src="<?= htmlspecialchars(getImagePath($img_src)) ?>"
                                     alt="Photo of <?= htmlspecialchars($hotel['hotel_name']) ?>"
                                     class="hotel-thumbnail"
                                     loading="lazy">
                            <?php endforeach; ?>
                        </div>
                        <div class="hotel-info">
                            <h3><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                            <p class="location"><?= htmlspecialchars($hotel['location']) ?></p>
                            <p class="price">₹<?= number_format($hotel['price'], 2) ?>/night</p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="bg-gray-800 p-4 rounded-lg mt-8">
                <h3 class="text-xl text-cyan-400 mb-2">Debug Information:</h3>
                <p>Found <?= count($hotels) ?> hotels</p>
                <p>Query: <?= isset($query) ? htmlspecialchars($query) : 'No query' ?></p>
                <p>
                    <a href="check_database.php" class="text-cyan-400 underline">Check Database</a> |
                    <a href="index.php?generate_images=1" class="text-cyan-400 underline">Generate Hotel Images</a>
                </p>
            </div>
        <?php endif; ?>

        <?php if (!empty($recently_viewed)): ?>
        <!-- Recently Viewed Hotels -->
        <div class="mt-12">
            <h3 class="text-center text-2xl mb-6 text-cyan-400">Recently Viewed</h3>
            <div class="hotel-list">
                <?php foreach ($recently_viewed as $hotel): ?>
                    <div class="hotel-card" onclick="window.location.href='hotel.php?id=<?= htmlspecialchars($hotel['id']) ?>'">
                        <div class="hotel-images-grid">
                            <!-- Main image from hotels table -->
                            <img src="<?= htmlspecialchars(getImagePath($hotel['image_path'] ?? $hotel['image'] ?? '')) ?>"
                                 alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                                 class="hotel-thumbnail"
                                 loading="lazy">
                        </div>
                        <div class="hotel-info">
                            <h3><?= htmlspecialchars($hotel['hotel_name']) ?></h3>
                            <p class="location"><?= htmlspecialchars($hotel['location']) ?></p>
                            <p class="price">₹<?= number_format($hotel['price'], 2) ?>/night</p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Include Cookie Consent Banner -->
    <?php include 'cookie_consent.php'; ?>

    <footer>
        <p>&copy; <?= date('Y') ?> Hotel Finder. Elevate Your Stay.</p>
    </footer>
</body>
</html>
