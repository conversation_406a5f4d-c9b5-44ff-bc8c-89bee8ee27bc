<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php"); // Redirect if not logged in
    exit();
}

include __DIR__ . '/db_connect.php'; // Include DB connection
require_once __DIR__ . '/utils.php'; // Include utilities

// Fetch user details from the database
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // Fetch hotels added by this user if they are an owner
    $user_hotels = [];
    if ($user && $user['user_type'] === 'owner') {
        $stmt = $pdo->prepare("SELECT * FROM hotels WHERE owner_id = ? ORDER BY id DESC");
        $stmt->execute([$_SESSION['user_id']]);
        $user_hotels = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Fetch additional images for each hotel
        foreach ($user_hotels as $key => $hotel) {
            $img_stmt = $pdo->prepare("SELECT * FROM hotel_images WHERE hotel_id = ?");
            $img_stmt->execute([$hotel['id']]);
            $user_hotels[$key]['additional_images'] = $img_stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 50px auto;
            background-color: #1e293b;
            border-radius: 15px;
            box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.3);
            padding: 30px;
        }
        .profile-info { margin-bottom: 15px; }
        .profile-info strong { color: #38bdf8; }
        a.logout-btn { color: #38bdf8; text-decoration: none; }

        /* Hotel cards styling */
        .my-hotels-section { margin-top: 40px; }
        .my-hotels-section h3 { color: #38bdf8; margin-bottom: 20px; font-size: 1.5rem; }
        .hotel-list {
            width: 100%;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .hotel-card {
            background-color: #334155;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* Hotel images grid */
        .hotel-images-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-auto-rows: 100px;
            gap: 5px;
            padding: 5px;
        }

        .hotel-image-container {
            overflow: hidden;
            border-radius: 5px;
        }

        /* Make first image larger */
        .hotel-images-grid .hotel-image-container:first-child {
            grid-column: span 3;
            grid-row: span 2;
        }

        .hotel-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .hotel-thumbnail:hover {
            transform: scale(1.05);
        }

        .hotel-info {
            padding: 15px;
        }
        .hotel-info h4 {
            color: #38bdf8;
            margin-bottom: 5px;
            font-size: 1.2rem;
        }
        .hotel-location {
            color: #94a3b8;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        .hotel-price {
            color: #38bdf8;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .hotel-actions {
            margin-top: 15px;
        }
        .view-btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            text-decoration: none;
            text-align: center;
        }
        .view-btn:hover {
            background-color: #0284c7;
        }
        .add-hotel-btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
            text-decoration: none;
        }
        .add-hotel-btn:hover {
            background-color: #0284c7;
        }
        .no-hotels {
            color: #94a3b8;
            font-style: italic;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="profile-container">
        <h2 style="color: #38bdf8; font-size: 1.8rem; margin-bottom: 15px;">Welcome, <?php echo htmlspecialchars($user['username']); ?>!</h2>
        <div class="profile-info"><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></div>
        <div class="profile-info"><strong>User Type:</strong> <?php echo htmlspecialchars($user['user_type']); ?></div>

        <?php if ($user['user_type'] === 'owner'): ?>
        <div class="my-hotels-section">
            <h3>My Hotels</h3>

            <?php if (empty($user_hotels)): ?>
                <p class="no-hotels">You haven't added any hotels yet.</p>
            <?php else: ?>
                <div class="hotel-list">
                    <?php foreach ($user_hotels as $hotel): ?>
                        <div class="hotel-card">
                            <div class="hotel-images-grid">
                                <!-- Main image -->
                                <div class="hotel-image-container">
                                    <img src="<?= htmlspecialchars(getImagePath(isset($hotel['image_path']) ? $hotel['image_path'] : (isset($hotel['image']) ? $hotel['image'] : ''))) ?>"
                                         alt="<?= htmlspecialchars($hotel['hotel_name']) ?>"
                                         class="hotel-thumbnail"
                                         loading="lazy">
                                </div>

                                <!-- Additional images -->
                                <?php foreach ($hotel['additional_images'] as $image): ?>
                                    <?php
                                    $img_src = '';
                                    if (isset($image['image'])) {
                                        $img_src = $image['image'];
                                    } else if (isset($image['image_path'])) {
                                        $img_src = $image['image_path'];
                                    }
                                    ?>
                                    <div class="hotel-image-container">
                                        <img src="<?= htmlspecialchars(getImagePath($img_src)) ?>"
                                             alt="Photo of <?= htmlspecialchars($hotel['hotel_name']) ?>"
                                             class="hotel-thumbnail"
                                             loading="lazy">
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="hotel-info">
                                <h4><?= htmlspecialchars($hotel['hotel_name']) ?></h4>
                                <p class="hotel-location"><?= htmlspecialchars($hotel['location']) ?></p>
                                <p class="hotel-price">₹<?= number_format($hotel['price'], 2) ?></p>
                                <div class="hotel-actions">
                                    <a href="hotel.php?id=<?= $hotel['id'] ?>" class="view-btn">View</a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <a href="add_hotel.php" class="add-hotel-btn">Add New Hotel</a>
        </div>
        <?php endif; ?>

        <div style="margin-top: 30px;">
            <a href="logout.php" class="logout-btn">Logout</a>
        </div>
    </div>
</body>
</html>
