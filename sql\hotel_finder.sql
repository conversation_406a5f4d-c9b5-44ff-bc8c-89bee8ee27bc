-- Drop existing tables if they exist
DROP TABLE IF EXISTS hotels;
DROP TABLE IF EXISTS users;

-- Create the users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('customer', 'owner') NOT NULL
);

-- Create the hotels table with owner_id as a foreign key
CREATE TABLE hotels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    owner_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    image VARCHAR(255),
    description TEXT,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Sample users
INSERT INTO users (username, email, password, user_type) VALUES
('owner1', '<EMAIL>', '$2y$10$examplehashedpassword12345', 'owner'),
('customer1', '<EMAIL>', '$2y$10$examplehashedpassword12345', 'customer');

-- Sample hotels with owner_id linked to users
INSERT INTO hotels (owner_id, name, location, price, image, description) VALUES
(1, 'Hotel Zenith', 'New York', 200.00, 'sample1.jpg', 'A luxurious hotel in the heart of NYC.'),
(1, 'Hotel Aurora', 'Los Angeles', 180.00, 'sample2.jpg', 'A charming stay with city views in LA.'),
(1, 'Hotel Odyssey', 'Miami', 220.00, 'sample3.jpg', 'Beachfront hotel with a vibrant atmosphere.');
