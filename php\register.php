<?php include __DIR__ . '/db_connect.php'; ?>  <!-- Ensure correct path -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Hotel Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Poppins', sans-serif; background-color: #0d0d0d; color: #e5e5e5; }
        header { background-color: #1a1a1a; padding: 20px; box-shadow: 0px 4px 15px rgba(0, 255, 255, 0.3); }
        header h1 { color: #00e5ff; font-size: 2.5rem; text-shadow: 0px 2px 5px rgba(0, 229, 255, 0.6); }
        nav a { margin: 0 15px; color: #e5e5e5; text-decoration: none; font-weight: 500; }
        nav a:hover { color: #00e5ff; }
        .container { max-width: 600px; margin: 50px auto; background-color: #1a1a1a; border-radius: 15px; box-shadow: 0px 8px 25px rgba(0, 255, 255, 0.2); padding: 30px; }
        h2 { color: #00e5ff; margin-bottom: 20px; text-align: center; }
        input, select { width: 100%; padding: 10px; margin-bottom: 15px; border-radius: 8px; border: 2px solid #00e5ff; background-color: #0d0d0d; color: #e5e5e5; }
        input::placeholder { color: #666; }
        input:focus, select:focus { outline: none; border-color: #0099cc; }
        input[type="submit"] { background-color: #00e5ff; color: #0d0d0d; font-weight: bold; cursor: pointer; transition: 0.3s; }
        input[type="submit"]:hover { background-color: #0099cc; }
        p { text-align: center; margin-top: 20px; }
        a { color: #00e5ff; text-decoration: none; }
        a:hover { color: #0099cc; }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <h1>Hotel Finder</h1>
        <nav>
            <a href="contact.php">Contact Us</a>
            
            <a href="login.php">Login</a>
            <a href="logout.php">Logout</a>
            <a href="index.php">Home</a>
        </nav>
    </header>

    <!-- Registration Form Container -->
    <div class="container">
        <h2>Register</h2>
        <form action="" method="POST">
            <input type="text" name="username" placeholder="Username" required>
            <input type="email" name="email" placeholder="Email" required>
            <input type="password" name="password" placeholder="Password" required>
            <select name="user_type" required>
                <option value="" disabled selected>Select User Type</option>
                <option value="customer">Customer</option>
                <option value="owner">Owner</option>
            </select>
            <input type="submit" name="register" value="Register">
        </form>

        <?php
        if (isset($_POST['register'])) {
            $username = $_POST['username'];
            $email = $_POST['email'];
            $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $user_type = $_POST['user_type'];

            try {
                $stmt = $pdo->prepare("INSERT INTO users (username, email, password, user_type) VALUES (?, ?, ?, ?)");
                $stmt->execute([$username, $email, $password, $user_type]);
                echo "<p>Registration successful! <a href='login.php'>Login Here</a></p>";
            } catch (PDOException $e) {
                echo "<p>Error: " . $e->getMessage() . "</p>";
            }
        }
        ?>
    </div>
</body>
</html>
