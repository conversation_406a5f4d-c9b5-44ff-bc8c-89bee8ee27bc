<?php
// This is the main setup script for the Hotel Finder website

// Check if GD library is available
if (!extension_loaded('gd')) {
    die("Error: GD library is not available. Please enable it in your PHP configuration.");
}

// Display setup page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Finder Setup</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #121212;
            color: #e5e5e5;
            font-family: 'Poppins', sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            background-color: #1a1a1a;
            border-radius: 15px;
            box-shadow: 0px 8px 25px rgba(0, 255, 255, 0.2);
            padding: 30px;
        }
        h1 {
            color: #00e5ff;
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0px 2px 5px rgba(0, 229, 255, 0.6);
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #222;
            border-radius: 10px;
        }
        .step h2 {
            color: #00e5ff;
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            background-color: #00e5ff;
            color: #0d0d0d;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            text-decoration: none;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0099cc;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hotel Finder Setup</h1>
        
        <div class="step">
            <h2>Step 1: Database Setup</h2>
            <p>This will create the necessary database and tables for the Hotel Finder website.</p>
            <a href="php/setup_database.php" class="btn">Setup Database</a>
        </div>
        
        <div class="step">
            <h2>Step 2: Create Sample Images</h2>
            <p>This will create sample hotel images for the website.</p>
            <a href="php/create_default_images.php" class="btn">Create Images</a>
        </div>
        
        <div class="step">
            <h2>Step 3: Go to Website</h2>
            <p>Once you've completed the setup, you can visit your Hotel Finder website.</p>
            <a href="index.php" class="btn">Go to Website</a>
        </div>
        
        <div class="mt-8 text-center text-sm">
            <p>Default login credentials:</p>
            <p><strong>Owner:</strong> username: owner1, password: owner123</p>
            <p><strong>User:</strong> username: user1, password: user123</p>
        </div>
    </div>
</body>
</html>
