<?php
// Only start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>

<header>
    <h1>Hotel Finder</h1>
    <nav>
        <a href="index.php">Home</a>
        <a href="search.php">Search</a>
        <a href="contact.php">Contact Us</a>

        <?php if (isset($_SESSION['user_id'])): ?>
            <a href="profile.php">My Profile</a>
            <?php if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'owner'): ?>
                <a href="add_hotel.php">Add Hotel</a>
            <?php endif; ?>
            <a href="logout.php">Logout</a>
        <?php else: ?>
            <a href="register.php">Register</a>
            <a href="login.php">Login</a>
        <?php endif; ?>
    </nav>
</header>
