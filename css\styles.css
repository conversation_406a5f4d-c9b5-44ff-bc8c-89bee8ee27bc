/* Modern Hotel Finder Theme */
body {
    background-color: #0f172a; /* Dark blue background */
    color: #e2e8f0;
    font-family: 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Container styling */
.container {
    max-width: 1200px;
    width: 90%;
    margin: 30px auto;
    padding: 25px;
    background-color: #1e293b; /* Slightly lighter blue */
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    color: #38bdf8; /* Light blue */
    margin-top: 0;
}

h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

/* Form elements */
form input[type="text"],
form input[type="password"],
form input[type="email"],
form input[type="number"],
form input[type="submit"],
form textarea,
select {
    width: 100%;
    padding: 12px;
    margin-bottom: 15px;
    border: 2px solid #38bdf8;
    border-radius: 8px;
    background-color: #0f172a;
    color: #e2e8f0;
    font-size: 1rem;
    transition: all 0.3s ease;
}

form input:focus,
form textarea:focus,
select:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.3);
}

form input[type="submit"] {
    background-color: #0ea5e9;
    color: white;
    cursor: pointer;
    font-weight: 600;
    border: none;
    transition: background-color 0.3s ease;
}

form input[type="submit"]:hover {
    background-color: #0284c7;
}

/* Search box */
.search-box {
    margin-bottom: 2rem;
}

.search-box input {
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 1rem;
}

.search-box button {
    background-color: #0ea5e9;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.search-box button:hover {
    background-color: #0284c7;
}

/* Hotel list */
.hotel-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

/* Hotel card */
.hotel-card {
    background-color: #334155;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    padding: 15px;
    border-left: 4px solid #0ea5e9;
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
}

/* Hotel images grid for cascading layout */
.hotel-images-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: 120px;
    gap: 10px;
    margin-bottom: 15px;
    width: 100%;
}

/* Make first image larger */
.hotel-images-grid img:first-child {
    grid-column: span 2;
    grid-row: span 2;
}

.hotel-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.3s, filter 0.3s, box-shadow 0.3s;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.hotel-thumbnail:hover {
    transform: scale(1.05);
    z-index: 1;
    filter: brightness(1.1) contrast(1.1);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.hotel-thumbnail::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(56, 189, 248, 0) 0%, rgba(56, 189, 248, 0.3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.hotel-thumbnail:hover::after {
    opacity: 1;
}

.hotel-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.hotel-card h3 {
    color: #38bdf8;
    font-size: 1.3rem;
    margin: 0 0 8px 0;
    font-weight: 600;
}

.hotel-card p {
    margin: 0 0 5px 0;
    color: #cbd5e1;
}

.hotel-card .location {
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.hotel-card .location:before {
    content: '📍';
    margin-right: 5px;
}

.hotel-card .price {
    color: #38bdf8;
    font-weight: bold;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.hotel-card .price:before {
    content: '💰';
    margin-right: 5px;
}

/* Header styling */
header {
    background-color: #1e293b;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

header h1 {
    color: #38bdf8;
    font-size: 2.5rem;
    text-align: center;
    margin: 0 0 15px 0;
    font-weight: 700;
}

nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
}

nav a {
    color: #e2e8f0;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

nav a:hover {
    background-color: #334155;
    color: #38bdf8;
}

/* Footer styling */
footer {
    background-color: #1e293b;
    padding: 20px;
    text-align: center;
    margin-top: 50px;
    color: #94a3b8;
    font-size: 0.9rem;
}

/* Debug information */
.bg-gray-800 {
    background-color: #1e293b;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    border-left: 4px solid #f59e0b;
}

.text-cyan-400 {
    color: #38bdf8;
}

/* Responsive design */
@media (max-width: 768px) {
    .hotel-card {
        flex-direction: column;
        align-items: flex-start;
    }

    .hotel-images-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    nav {
        flex-direction: column;
        align-items: center;
    }

    nav a {
        width: 100%;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hotel-images-grid {
        grid-template-columns: 1fr;
    }

    .hotel-images-grid img:first-child {
        grid-column: 1;
        grid-row: span 1;
    }
}

