# Hotel Finder Website

A fully functional hotel booking website with user authentication, hotel listings, search functionality, and cookie management.

## Features

- User authentication (login/register)
- Different user roles (regular users and hotel owners)
- Hotel listings with details and images
- Search functionality
- Recently viewed hotels tracking with cookies
- Cookie consent management
- Responsive design

## Setup Instructions

1. Make sure you have XAMPP installed and running (Apache and MySQL)
2. Place the project files in your XAMPP htdocs directory (e.g., `C:\xampp\htdocs\hotel_finder`)
3. Open your browser and navigate to `http://localhost/hotel_finder/setup.php`
4. Follow the setup steps:
   - Set up the database
   - Create sample images
   - Go to the website

## Default Login Credentials

- **Hotel Owner**:
  - Username: owner1
  - Password: owner123

- **Regular User**:
  - Username: user1
  - Password: user123

## Directory Structure

- `/css` - Stylesheet files
- `/images` - Hotel images and default images
- `/js` - JavaScript files
- `/php` - PHP files for the website functionality
- `/sql` - SQL files for database setup

## Technologies Used

- PHP
- MySQL
- HTML/CSS
- JavaScript
- Tailwind CSS

## License

This project is open-source and available for personal and educational use.
