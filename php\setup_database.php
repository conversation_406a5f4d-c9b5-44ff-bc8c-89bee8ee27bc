<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Connect to MySQL server
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS hotel_finder");
    echo "Database 'hotel_finder' created or already exists.<br>";

    // Select the database
    $pdo->exec("USE hotel_finder");

    // Create tables
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            user_type ENUM('user', 'owner') NOT NULL DEFAULT 'user',
            remember_token VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "Table 'users' created or already exists.<br>";

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS hotels (
            id INT AUTO_INCREMENT PRIMARY KEY,
            owner_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(255) NOT NULL,
            price_per_night DECIMAL(10,2) NOT NULL,
            description TEXT,
            image VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (owner_id) REFERENCES users(id)
        )
    ");
    echo "Table 'hotels' created or already exists.<br>";

    $pdo->exec("
        CREATE TABLE IF NOT EXISTS hotel_images (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hotel_id INT NOT NULL,
            image_path VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (hotel_id) REFERENCES hotels(id) ON DELETE CASCADE
        )
    ");
    echo "Table 'hotel_images' created or already exists.<br>";

    // Check if there are any users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();

    // Insert sample data if no users exist
    if ($userCount == 0) {
        // Insert sample users
        $ownerPassword = password_hash('owner123', PASSWORD_DEFAULT);
        $userPassword = password_hash('user123', PASSWORD_DEFAULT);

        $pdo->exec("
            INSERT INTO users (username, email, password, user_type) VALUES
            ('owner1', '<EMAIL>', '$ownerPassword', 'owner'),
            ('user1', '<EMAIL>', '$userPassword', 'user')
        ");
        echo "Sample users created.<br>";

        // Get the owner ID
        $stmt = $pdo->query("SELECT id FROM users WHERE username = 'owner1'");
        $ownerId = $stmt->fetchColumn();

        // Insert sample hotels
        $pdo->exec("
            INSERT INTO hotels (owner_id, name, location, price_per_night, description, image) VALUES
            ($ownerId, 'Luxury Resort & Spa', 'Miami, FL', 299.99, 'Experience luxury like never before at our beachfront resort. Featuring stunning ocean views, a world-class spa, and exquisite dining options.', '../images/hotel1.jpg'),
            ($ownerId, 'Downtown Boutique Hotel', 'New York, NY', 199.99, 'Located in the heart of Manhattan, our boutique hotel offers stylish accommodations with easy access to major attractions, shopping, and dining.', '../images/hotel2.jpg'),
            ($ownerId, 'Mountain View Lodge', 'Aspen, CO', 249.99, 'Nestled in the mountains, our lodge provides a cozy retreat with breathtaking views, hiking trails, and a rustic yet elegant atmosphere.', '../images/hotel3.jpg'),
            ($ownerId, 'Seaside Inn', 'San Diego, CA', 179.99, 'Enjoy the perfect beach getaway at our charming inn. Just steps from the ocean with comfortable rooms and a relaxed coastal vibe.', '../images/hotel4.jpg')
        ");
        echo "Sample hotels created.<br>";
    } else {
        echo "Sample data already exists. Skipping data creation.<br>";
    }

    echo "<p>Database setup completed successfully!</p>";
    echo "<p><a href='../index.php'>Go to Homepage</a></p>";

} catch (PDOException $e) {
    die("Database Error: " . $e->getMessage());
}
?>
